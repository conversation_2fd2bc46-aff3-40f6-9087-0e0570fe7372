// @ts-nocheck
import './Statistics.scss'

import { useEffect, useState } from 'react'
import { useAppSelector } from '../../Hooks'

interface StatisticsData {
  totalPosts: number
  totalArticles: number
  totalPhotos: number
  totalVideos: number
}

const Statistics = () => {
  const userState: any = useAppSelector(state => state.user)
  const [statistics, setStatistics] = useState<StatisticsData>({
    totalPosts: 0,
    totalArticles: 0,
    totalPhotos: 0,
    totalVideos: 0
  })

  // Mock data for now - this can be replaced with actual API calls later
  useEffect(() => {
    if (userState?.value?.data?._id) {
      // Simulate fetching statistics data
      // In a real implementation, this would be an API call
      setStatistics({
        totalPosts: 24,
        totalArticles: 8,
        totalPhotos: 156,
        totalVideos: 12
      })
    }
  }, [userState?.value?.data?._id])

  const statisticsCards = [
    {
      title: 'Total Posts',
      count: statistics.totalPosts,
      icon: 'fa fa-edit',
      color: '#F8B195'
    },
    {
      title: 'Total Articles',
      count: statistics.totalArticles,
      icon: 'fa fa-file-text',
      color: '#355C7D'
    },
    {
      title: 'Total Photos',
      count: statistics.totalPhotos,
      icon: 'fa fa-camera',
      color: '#6C7A89'
    },
    {
      title: 'Total Videos',
      count: statistics.totalVideos,
      icon: 'fa fa-video-camera',
      color: '#29cc97'
    }
  ]

  if (!userState?.value?.data?._id) {
    return null
  }

  return (
    <div className="Statistics">
      <div className="statistics-header">
        <h2>Dashboard Overview</h2>
      </div>
      <div className="statistics-grid">
        {statisticsCards.map((card, index) => (
          <div key={index} className="statistics-card">
            <div className="card-icon" style={{ color: card.color }}>
              <i className={card.icon}></i>
            </div>
            <div className="card-content">
              <div className="card-count">{card.count}</div>
              <div className="card-title">{card.title}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Statistics
