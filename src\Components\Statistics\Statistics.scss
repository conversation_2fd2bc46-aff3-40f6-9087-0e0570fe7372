@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/mixins';

.Statistics {
  padding: 0 80px 30px;
  width: 100%;

  @include media-breakpoint-down(md) {
    padding: 0 15px 30px;
  }

  @include media-breakpoint-only(md) {
    padding: 0 40px 30px;
  }

  .statistics-header {
    margin-bottom: 20px;

    h2 {
      font: {
        family: $font-family-base;
        weight: $font-weight-medium;
        size: 24px;
      }
      color: $heading;
      margin: 0;

      @include media-breakpoint-down(md) {
        font-size: 20px;
      }
    }
  }

  .statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    @include media-breakpoint-down(md) {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: 1fr;
      gap: 15px;
    }
  }

  .statistics-card {
    background: $bg;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    min-height: 80px;

    @include media-breakpoint-down(md) {
      padding: 15px;
      gap: 12px;
      min-height: 70px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 0.2);
    }

    .card-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;

      @include media-breakpoint-down(md) {
        width: 35px;
        height: 35px;
      }

      i {
        font-size: 18px;

        @include media-breakpoint-down(md) {
          font-size: 16px;
        }
      }
    }

    .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2px;

      .card-count {
        font: {
          family: $font-family-base;
          weight: $font-weight-bold;
          size: 28px;
        }
        color: $white;
        line-height: 1;

        @include media-breakpoint-down(md) {
          font-size: 24px;
        }

        @include media-breakpoint-down(sm) {
          font-size: 20px;
        }
      }

      .card-title {
        font: {
          family: $font-family-base;
          weight: $font-weight-regular;
          size: 14px;
        }
        color: $paragraph-2;
        line-height: 1.2;

        @include media-breakpoint-down(md) {
          font-size: 12px;
        }
      }
    }
  }

  // Animation for loading state
  .statistics-card.loading {
    .card-count {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      height: 28px;
      width: 60px;
    }

    .card-title {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      height: 14px;
      width: 80px;
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}
